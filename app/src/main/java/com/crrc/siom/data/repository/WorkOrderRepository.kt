package com.crrc.siom.data.repository

import android.annotation.SuppressLint
import com.crrc.common.BaseResponse
import com.crrc.common.bean.response.PlanOrderListResponse
import com.crrc.common.bean.response.FaultOrderListResponse
import com.crrc.common.bean.response.EmergencyOrderListResponse
import com.crrc.common.bean.response.OrderFilterParamResponse
import com.crrc.common.bean.response.TempOrderListResponse
import com.crrc.common.bean.response.WorkOrderCountResponse
import com.crrc.common.bean.response.WorkOrderDetailResponse
import com.crrc.common.bean.response.MembersResponse
import com.crrc.common.bean.response.PickMaterialResponse
import com.crrc.common.utils.GetRequestBody
import com.crrc.network.NetworkApi
import com.crrc.network.api.ApiInterface
import com.crrc.network.errorhandler.ExceptionHandle
import com.crrc.network.observer.BaseObserverForBasic

interface WorkOrderRepository {
    fun getPlannedWorkOrders(pageNum: Int, pageSize: Int, status: String? = null,
                             station: String? = null,
                             type: String? = null,
                             priority: String? = null,callback: (List<PlanOrderListResponse.PlanOrderRecord>?, String?) -> Unit)
    
    fun getTemporaryWorkOrders(page: Int, pageSize: Int, callback: (List<TempOrderListResponse.TempOrderRecord>?, String?) -> Unit)
    
    fun getEmergencyWorkOrders(page: Int, pageSize: Int, callback: (List<EmergencyOrderListResponse.EmergencyOrderRecord>?, String?) -> Unit)
    
    fun getFaultWorkOrders(page: Int, pageSize: Int, callback: (List<FaultOrderListResponse.FaultOrderRecord>?, String?) -> Unit)
    
    fun getWorkOrderCount(callback: (WorkOrderCountResponse?, String?) -> Unit)
    
    fun getWorkOrderDetail(id: String, callback: (WorkOrderDetailResponse?, String?) -> Unit)
    
    fun getPickMaterialList(
        workOrderId: String,
        standbyIds: List<Int>,
        toolIds: List<Int>,
        note: String,
        callback: (PickMaterialResponse?, String?) -> Unit
    )
    
    fun rollbackWorkOrder(id: String, callback: (Boolean, String?) -> Unit)
    
    fun assignOpt(workOrderId: String, callback: (MembersResponse?, String?) -> Unit)
    fun assign(workOrderId: String,members: List<MembersResponse.FilterParam>, callback: (Boolean, String?) -> Unit)
    fun cancelWorkOrder(workOrderId: String, callback: (Boolean, String?) -> Unit)
    fun acceptWorkOrder(workOrderId: String, callback: (Boolean, String?) -> Unit)
    fun arriveWorkOrder(workOrderId: String, callback: (Boolean, String?) -> Unit)
    fun dealWorkOrder(workOrderId: String, callback: (Boolean, String?) -> Unit)

    fun confirmWorkOrder(workOrderId: String, callback: (Boolean, String?) -> Unit)

    fun getFilterParamList(callback: (OrderFilterParamResponse?, String?) -> Unit)
}

@SuppressLint("CheckResult")
class WorkOrderRepositoryImpl : WorkOrderRepository {
    override fun getFilterParamList( callback: (OrderFilterParamResponse?, String?) -> Unit){
        NetworkApi.getService(ApiInterface::class.java)
            .getFilterParamList()
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<OrderFilterParamResponse>>() {
                override fun onSuccess(response: BaseResponse<OrderFilterParamResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun getPlannedWorkOrders(
        pageNum: Int,
        pageSize: Int,
        status: String?,
        station: String?,
        type: String?,
        priority: String?,
        callback: (List<PlanOrderListResponse.PlanOrderRecord>?, String?) -> Unit
    ) {
        val map = HashMap<String, Any>()
        map["pageNum"] = pageNum
        map["pageSize"] = pageSize
        status?.let { map["status"] = it }
        station?.let { map["station"] = it }
        type?.let { map["type"] = it }
        priority?.let { map["priority"] = it }
        NetworkApi.getService(ApiInterface::class.java)
            .getPlanOrderList(GetRequestBody.requestBody(map))
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<PlanOrderListResponse>>() {
                override fun onSuccess(response: BaseResponse<PlanOrderListResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data.records, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun getTemporaryWorkOrders(pageNum: Int, pageSize: Int, callback: (List<TempOrderListResponse.TempOrderRecord>?, String?) -> Unit) {
        val map = HashMap<String, Any>()
        map["pageNum"] = pageNum
        map["pageSize"] = pageSize
        NetworkApi.getService(ApiInterface::class.java)
            .getTempOrderList(GetRequestBody.requestBody(map))
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<TempOrderListResponse>>() {
                override fun onSuccess(response: BaseResponse<TempOrderListResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data.records, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun getEmergencyWorkOrders(pageNum: Int, pageSize: Int, callback: (List<EmergencyOrderListResponse.EmergencyOrderRecord>?, String?) -> Unit) {
        val map = HashMap<String, Any>()
        map["pageNum"] = pageNum
        map["pageSize"] = pageSize
        NetworkApi.getService(ApiInterface::class.java)
            .getEmergencyOrderList(GetRequestBody.requestBody(map))
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<EmergencyOrderListResponse>>() {
                override fun onSuccess(response: BaseResponse<EmergencyOrderListResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data.records, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun getFaultWorkOrders(pageNum: Int, pageSize: Int, callback: (List<FaultOrderListResponse.FaultOrderRecord>?, String?) -> Unit) {
        val map = HashMap<String, Any>()
        map["pageNum"] = pageNum
        map["pageSize"] = pageSize
        NetworkApi.getService(ApiInterface::class.java)
            .getFaultOrderList(GetRequestBody.requestBody(map))
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<FaultOrderListResponse>>() {
                override fun onSuccess(response: BaseResponse<FaultOrderListResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data.records, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun getWorkOrderCount(callback: (WorkOrderCountResponse?, String?) -> Unit) {
        NetworkApi.getService(ApiInterface::class.java)
            .getWorkOrderCount()
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<WorkOrderCountResponse>>() {
                override fun onSuccess(response: BaseResponse<WorkOrderCountResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun getWorkOrderDetail(workOrderId: String, callback: (WorkOrderDetailResponse?, String?) -> Unit) {
        val map = HashMap<String, Any>()
        map["id"] = workOrderId
        NetworkApi.getService(ApiInterface::class.java)
            .getWorkOrderDetail(GetRequestBody.requestBody(map))
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<WorkOrderDetailResponse>>() {
                override fun onSuccess(response: BaseResponse<WorkOrderDetailResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun getPickMaterialList(
        workOrderId: String,
        standbyIds: List<Int>,
        toolIds: List<Int>,
        note: String,
        callback: (PickMaterialResponse?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getPickMaterialList(workOrderId)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<PickMaterialResponse>>() {
                override fun onSuccess(response: BaseResponse<PickMaterialResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }

                override fun onFail(e: Throwable) {
                    callback(null, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun rollbackWorkOrder(workOrderId: String, callback: (Boolean, String?) -> Unit) {
        NetworkApi.getService(ApiInterface::class.java)
            .rollbackWorkOrder(workOrderId)
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Any>>() {
                override fun onSuccess(response: BaseResponse<Any>) {
                    callback(true, null)
                }

                override fun onFail(e: Throwable) {
                    callback(false, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun cancelWorkOrder(workOrderId: String, callback: (Boolean, String?) -> Unit) {
        val map = HashMap<String, Any>()
        map["id"] = workOrderId
        NetworkApi.getService(ApiInterface::class.java)
            .cancelWorkOrder(GetRequestBody.requestBody(map))
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Any>>() {
                override fun onSuccess(response: BaseResponse<Any>) {
                    callback(true, null)
                }

                override fun onFail(e: Throwable) {
                    callback(false, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun acceptWorkOrder(
        workOrderId: String,
        callback: (Boolean, String?) -> Unit
    ) {
        val map = HashMap<String, Any>()
        map["id"] = workOrderId
        NetworkApi.getService(ApiInterface::class.java)
            .acceptWorkOrder(GetRequestBody.requestBody(map))
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Any>>() {
                override fun onSuccess(response: BaseResponse<Any>) {
                    callback(true, null)
                }

                override fun onFail(e: Throwable) {
                    callback(false, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun arriveWorkOrder(
        workOrderId: String,
        callback: (Boolean, String?) -> Unit
    ) {
        val map = HashMap<String, Any>()
        map["id"] = workOrderId
        NetworkApi.getService(ApiInterface::class.java)
            .arriveWorkOrder(GetRequestBody.requestBody(map))
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Any>>() {
                override fun onSuccess(response: BaseResponse<Any>) {
                    callback(true, null)
                }

                override fun onFail(e: Throwable) {
                    callback(false, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun dealWorkOrder(
        workOrderId: String,
        callback: (Boolean, String?) -> Unit
    ) {
        val map = HashMap<String, Any>()
        map["id"] = workOrderId
        NetworkApi.getService(ApiInterface::class.java)
            .dealWorkOrder(GetRequestBody.requestBody(map))
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Any>>() {
                override fun onSuccess(response: BaseResponse<Any>) {
                    callback(true, null)
                }

                override fun onFail(e: Throwable) {
                    callback(false, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun confirmWorkOrder(
        workOrderId: String,
        callback: (Boolean, String?) -> Unit
    ) {
        val map = HashMap<String, Any>()
        map["id"] = workOrderId
        NetworkApi.getService(ApiInterface::class.java)
            .confirmWorkOrder(GetRequestBody.requestBody(map))
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Any>>() {
                override fun onSuccess(response: BaseResponse<Any>) {
                    callback(true, null)
                }

                override fun onFail(e: Throwable) {
                    callback(false, ExceptionHandle.handleException(e).message)
                }
            }))
    }

    override fun assignOpt(workOrderId: String, callback: (MembersResponse?, String?) -> Unit) {
        val map = HashMap<String, Any>()
        map["id"] = workOrderId
        NetworkApi.getService(ApiInterface::class.java)
            .assignOpt(GetRequestBody.requestBody(map))
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<MembersResponse>>() {
                override fun onSuccess(response: BaseResponse<MembersResponse>) {
                    val data = response.data
                    if (data != null) {
                        callback(data, null)
                    } else {
                        callback(null, "数据为空")
                    }
                }
                override fun onFail(e: Throwable) {
                    callback(null, ExceptionHandle.handleException(e).message)
                }
            }))
    }
    override fun assign(workOrderId: String, members: List<MembersResponse.FilterParam>, callback: (Boolean, String?) -> Unit) {
        val map = HashMap<String, Any>()
        map["id"] = workOrderId
        map["members"] = members
        NetworkApi.getService(ApiInterface::class.java)
            .assign(GetRequestBody.requestBody(map))
            .compose(NetworkApi.getInstance().applySchedulers(object : BaseObserverForBasic<BaseResponse<Any>>() {
                override fun onSuccess(response: BaseResponse<Any>) {
                    callback(true, null)
                }

                override fun onFail(e: Throwable) {
                    callback(false, ExceptionHandle.handleException(e).message)
                }
            }))
    }
} 