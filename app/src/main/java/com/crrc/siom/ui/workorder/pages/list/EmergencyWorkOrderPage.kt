package com.crrc.siom.ui.workorder.pages.list

import android.widget.Toast
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.EmergencyOrderListResponse.EmergencyOrderRecord
import com.crrc.siom.ui.components.CustomDialog
import com.crrc.siom.ui.workorder.components.FilterBar
import com.crrc.siom.ui.workorder.components.BaseWorkOrderPage
import com.crrc.siom.ui.workorder.components.WorkOrderCard
import com.crrc.siom.ui.workorder.viewmodel.EmergencyWorkOrderViewModel
import com.crrc.siom.ui.workorder.viewmodel.FilterViewModel

@Composable
fun EmergencyWorkOrderPage(
    viewModel: EmergencyWorkOrderViewModel = viewModel(),
    filterViewModel: FilterViewModel = viewModel(key = "emergency_filter")

) {
    val workOrders by viewModel.workOrders.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val isLoadingMore by viewModel.isLoadingMore.collectAsState()
    val hasMoreData by viewModel.hasMoreData.collectAsState()
    val error by viewModel.error.collectAsState()
    // 回退对话框状态
    var showRollbackDialog by remember { mutableStateOf(false) }
    var selectedWorkOrderId by remember { mutableStateOf("") }
    val isRollbackLoading by viewModel.isRollbackLoading.collectAsState()
    val rollbackResult by viewModel.rollbackResult.collectAsState()

    // 作废对话框状态
    var showCancelDialog by remember { mutableStateOf(false) }
    var selectedWorkOrderIdForCancel by remember { mutableStateOf("") }
    val isCancelLoading by viewModel.isCancelLoading.collectAsState()
    val cancelResult by viewModel.cancelResult.collectAsState()

    // 处理回退结果
    val context = LocalContext.current
    LaunchedEffect(rollbackResult) {
        rollbackResult?.let { (success, message) ->
            if (success) {
                Toast.makeText(context, "工单回退成功", Toast.LENGTH_SHORT).show()
                viewModel.refresh()
            } else {
                Toast.makeText(context, "工单回退失败: $message", Toast.LENGTH_SHORT).show()
            }
            viewModel.resetRollbackResult()
        }
    }

    // 处理作废结果
    LaunchedEffect(cancelResult) {
        cancelResult?.let { (success, message) ->
            if (success) {
                Toast.makeText(context, "工单作废成功", Toast.LENGTH_SHORT).show()
                // 刷新工单列表
                viewModel.refresh()
            } else {
                Toast.makeText(context, "工单作废失败: $message", Toast.LENGTH_SHORT).show()
            }
            viewModel.resetCancelResult()
        }
    }

    // 回退对话框
    if (showRollbackDialog) {
        CustomDialog(
            title = "工单回退",
            message = "确定要回退该工单吗？",
            onDismiss = { showRollbackDialog = false },
            onConfirm = {
                viewModel.rollbackWorkOrder(selectedWorkOrderId)
                showRollbackDialog = false
            },
            isLoading = isRollbackLoading
        )
    }

    // 作废对话框
    if (showCancelDialog) {
        CustomDialog(
            title = "工单作废",
            message = "确定要作废该工单吗？",
            onDismiss = { showCancelDialog = false },
            onConfirm = {
                viewModel.cancelWorkOrder(selectedWorkOrderIdForCancel)
                showCancelDialog = false
            },
            isLoading = isCancelLoading
        )
    }


    BaseWorkOrderPage(
        items = workOrders,
        isLoading = isLoading,
        isLoadingMore = isLoadingMore,
        hasMoreData = hasMoreData,
        error = error,
        onRefresh = { viewModel.refresh() },
        onLoadMore = { viewModel.loadMore() },
        filterBar = {
            FilterBar(
                onFilterChanged = { filterParams ->
                    viewModel.setFilterParams(filterParams)
                },
                filterViewModel = filterViewModel
            )
        },
        itemContent = { workOrder ->
            EmergencyWorkOrderCard(
                workOrder = workOrder,
                onRollback = { id ->
                    selectedWorkOrderId = id
                    showRollbackDialog = true
                },
                onCancel = { id ->
                    selectedWorkOrderIdForCancel = id
                    showCancelDialog = true
                }
            )
        }
    )
}

@Composable
private fun EmergencyWorkOrderCard(
    workOrder: EmergencyOrderRecord,
    modifier: Modifier = Modifier,
    onRollback: (String) -> Unit,
    onCancel: (String) -> Unit
) {
    WorkOrderCard(
        workOrder = workOrder,
        onClick = { /* TODO: 处理点击 */ },
        onAccept = { /* TODO: 处理接单 */ },
        onAssign = { /* TODO: 处理指派 */ },
        onReturn = { onRollback(workOrder.id) },
        onVoid = { onCancel(workOrder.id) },
        onMaterial = { /* TODO: 处理领料 */ },
        onArrive = { /* TODO: 处理到场 */ },
        onProcess = { /* TODO: 处理处理 */ },
        onComplete = { /* TODO: 处理完成 */ },
        modifier = modifier
    )
}

