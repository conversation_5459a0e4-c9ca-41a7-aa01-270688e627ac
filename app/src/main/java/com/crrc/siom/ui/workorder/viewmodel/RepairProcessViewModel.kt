package com.crrc.siom.ui.workorder.viewmodel

import androidx.lifecycle.ViewModel
import com.crrc.common.bean.response.ProcedureResponse
import com.crrc.siom.data.repository.RepairProcessRepository
import com.crrc.siom.data.repository.RepairProcessRepositoryImpl
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class RepairProcessViewModel(
    private val repairProcessRepository: RepairProcessRepository = RepairProcessRepositoryImpl()
) : ViewModel() {

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _procedureData = MutableStateFlow<ProcedureResponse?>(null)
    val procedureData: StateFlow<ProcedureResponse?> = _procedureData.asStateFlow()

    private val _operationResult = MutableStateFlow<Pair<Boolean, String?>?>(null)
    val operationResult: StateFlow<Pair<Boolean, String?>?> = _operationResult.asStateFlow()

    fun loadProcedure(workOrderId: String) {
        _isLoading.value = true
        repairProcessRepository.procedure(workOrderId) { response, error ->
            _isLoading.value = false
            _procedureData.value = response
            if (error != null) {
                _operationResult.value = Pair(false, error)
            }
        }
    }

    fun confirmProcedure(procedureResponse: ProcedureResponse) {
        _isLoading.value = true
        repairProcessRepository.procedureConfirm(procedureResponse) { success, error ->
            _isLoading.value = false
            _operationResult.value = Pair(success == true, error)
        }
    }

    fun passProcedure(workOrderId: String, procedureId: String) {
        _isLoading.value = true
        repairProcessRepository.procedurePass(workOrderId, procedureId) { success, error ->
            _isLoading.value = false
            _operationResult.value = Pair(success == true, error)
        }
    }

    fun failProcedure(workOrderId: String, procedureId: String) {
        _isLoading.value = true
        repairProcessRepository.procedureFail(workOrderId, procedureId) { success, error ->
            _isLoading.value = false
            _operationResult.value = Pair(success == true, error)
        }
    }

    fun resetOperationResult() {
        _operationResult.value = null
    }
}