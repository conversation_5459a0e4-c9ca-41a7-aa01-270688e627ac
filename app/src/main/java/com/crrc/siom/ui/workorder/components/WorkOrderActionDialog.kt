package com.crrc.siom.ui.components

import android.widget.Toast
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext

/**
 * 工单操作类型
 */
sealed class WorkOrderAction(
    val title: String,
    val message: String,
    val successMessage: String,
    val failureMessagePrefix: String
) {
    object Rollback : WorkOrderAction(
        title = "工单回退",
        message = "确定要回退该工单吗？",
        successMessage = "工单回退成功",
        failureMessagePrefix = "工单回退失败"
    )
    
    object Cancel : WorkOrderAction(
        title = "工单作废", 
        message = "确定要作废该工单吗？",
        successMessage = "工单作废成功",
        failureMessagePrefix = "工单作废失败"
    )
}

/**
 * 工单操作对话框状态
 */
@Stable
class WorkOrderActionDialogState {
    var isVisible by mutableStateOf(false)
        private set
    var selectedWorkOrderId by mutableStateOf("")
        private set
    
    fun show(workOrderId: String) {
        selectedWorkOrderId = workOrderId
        isVisible = true
    }
    
    fun hide() {
        isVisible = false
        selectedWorkOrderId = ""
    }
}

/**
 * 记住工单操作对话框状态
 */
@Composable
fun rememberWorkOrderActionDialogState(): WorkOrderActionDialogState {
    return remember { WorkOrderActionDialogState() }
}

/**
 * 工单操作对话框组件
 * 
 * @param state 对话框状态
 * @param action 操作类型
 * @param isLoading 是否正在加载
 * @param onAction 执行操作的回调，参数为工单ID
 */
@Composable
fun WorkOrderActionDialog(
    state: WorkOrderActionDialogState,
    action: WorkOrderAction,
    isLoading: Boolean,
    onAction: (String) -> Unit
) {
    if (state.isVisible) {
        CustomDialog(
            title = action.title,
            message = action.message,
            onDismiss = { state.hide() },
            onConfirm = {
                onAction(state.selectedWorkOrderId)
                state.hide()
            },
            isLoading = isLoading
        )
    }
}

/**
 * 处理工单操作结果的副作用
 * 
 * @param result 操作结果，Pair<Boolean, String>格式，第一个参数表示是否成功，第二个参数表示消息
 * @param action 操作类型
 * @param onSuccess 成功时的回调
 * @param onResetResult 重置结果的回调
 */
@Composable
fun HandleWorkOrderActionResult(
    result: Pair<Boolean, String>?,
    action: WorkOrderAction,
    onSuccess: () -> Unit = {},
    onResetResult: () -> Unit
) {
    val context = LocalContext.current
    
    LaunchedEffect(result) {
        result?.let { (success, message) ->
            if (success) {
                Toast.makeText(context, action.successMessage, Toast.LENGTH_SHORT).show()
                onSuccess()
            } else {
                Toast.makeText(context, "${action.failureMessagePrefix}: $message", Toast.LENGTH_SHORT).show()
            }
            onResetResult()
        }
    }
}
